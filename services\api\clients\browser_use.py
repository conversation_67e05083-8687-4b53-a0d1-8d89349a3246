"""
Browser-Use client for LinkedIn profile extraction.

This module provides functionality to extract LinkedIn profiles using the browser-use
Python package for web automation and Azure OpenAI for intelligent extraction.
"""

import asyncio
import json
import os
from typing import Optional, Dict, Any
from pydantic import SecretStr

from browser_use import Agent, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>onfig, Controller
from langchain_openai import AzureChatOpenAI

from common.models import LinkedInProfileData
from common.settings import settings
from common.logging import get_logger

logger = get_logger(__name__)


class BrowserUseClient:
    """
    Client for extracting LinkedIn profiles using browser automation.
    
    This client uses the browser-use package to automate Chrome browser
    interactions and Azure OpenAI for intelligent data extraction.
    """
    
    def __init__(self):
        """Initialize the browser-use client with Azure OpenAI configuration."""
        self.llm = None
        self._initialize_llm()
    
    def _initialize_llm(self):
        """Initialize Azure OpenAI LLM for the browser agent."""
        try:
            import os
            from dotenv import load_dotenv
            load_dotenv()

            self.llm = AzureChatOpenAI(
                model=os.getenv('AZURE_OPENAI_DEPLOYMENT_NAME', ''),
                api_version=os.getenv('AZURE_OPENAI_API_VERSION', '2024-02-15-preview'),
                azure_endpoint=os.getenv('AZURE_OPENAI_ENDPOINT', ''),
                api_key=SecretStr(os.getenv('AZURE_OPENAI_API_KEY', '')),
                temperature=float(os.getenv('AZURE_OPENAI_TEMPERATURE', '0.0')),
            )
            logger.info("Azure OpenAI LLM initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Azure OpenAI LLM: {e}")
            raise
    
    async def extract_linkedin_profile(self, profile_url: str) -> Optional[Dict[str, Any]]:
        """
        Extract LinkedIn profile data using browser automation.
        
        Args:
            profile_url: The LinkedIn profile URL to extract
            
        Returns:
            Dictionary containing extracted profile data, or None if extraction fails
        """
        logger.info(f"Starting LinkedIn profile extraction for: {profile_url}")
        
        browser = None
        try:
            # Initialize browser with configuration
            logger.info("Initializing browser...")
            browser = Browser(
                config=BrowserConfig(
                    headless=True,  # Run headless in production
                    disable_security=True,
                    chrome_instance_path=None,  # Let it find Chrome automatically
                )
            )
            
            # Create controller with our data model
            controller = Controller(output_model=LinkedInProfileData)
            
            # Define the extraction task
            task = self._create_extraction_task(profile_url)
            
            # Sensitive data for login
            sensitive_data = {
                'username': settings.linkedin.email,
                'password': settings.linkedin.password.get_secret_value()
            }
            
            # Create and run the agent
            logger.info("Running extraction agent...")
            agent = Agent(
                task=task,
                llm=self.llm,
                browser=browser,
                controller=controller,
                sensitive_data=sensitive_data
            )
            
            # Execute the extraction
            history = await agent.run()
            result = history.final_result()
            
            if not result:
                logger.warning("Agent returned empty result")
                return None
            
            # Parse and validate the result
            try:
                if isinstance(result, str):
                    linkedin_data = LinkedInProfileData.model_validate_json(result)
                else:
                    linkedin_data = LinkedInProfileData.model_validate(result)
                
                logger.info(
                    f"Successfully extracted profile for: {linkedin_data.full_name}",
                    profile_url=profile_url,
                    work_experience_count=len(linkedin_data.work_experience),
                    education_count=len(linkedin_data.education),
                    skills_count=len(linkedin_data.skills)
                )
                
                return linkedin_data.model_dump()
                
            except Exception as e:
                logger.error(f"Failed to parse extraction result: {e}", raw_result=str(result))
                return None
                
        except Exception as e:
            logger.error(f"Error during LinkedIn profile extraction: {e}", profile_url=profile_url)
            return None
            
        finally:
            # Ensure browser is closed
            if browser:
                try:
                    await browser.close()
                    logger.info("Browser closed successfully")
                except Exception as e:
                    logger.warning(f"Error closing browser: {e}")
    
    def _create_extraction_task(self, profile_url: str) -> str:
        """
        Create the detailed extraction task for the browser agent.
        
        Args:
            profile_url: The LinkedIn profile URL to extract
            
        Returns:
            Detailed task string for the browser agent
        """
        return f"""
        You are a LinkedIn profile data extractor. Follow these steps carefully:

        1. Navigate to https://www.linkedin.com/login
        2. Enter the username/email: {settings.linkedin.email}
        3. Enter the password: {settings.linkedin.password.get_secret_value()}
        4. Click the Sign In button and wait for login to complete
        5. Navigate to: {profile_url}
        6. Wait for the profile page to fully load
        7. Scroll down slowly to load all sections
        8. Click all "Show more", "See more", or similar buttons in:
            - Extract all experiences from {profile_url}/details/experience/
            - Extract all education from {profile_url}/details/education/
            - Extract all certifications from {profile_url}/details/certifications/
            - Extract all volunteer experiences from {profile_url}/details/volunteering-experiences/
            - Extract all skills from {profile_url}/details/skills/
        9. Extract the following fields:
            - Full name
            - Headline
            - Location
            - About summary
            - Languages
        10. Strictly avoid the navigation to any other page epsecially to the company or institution pages.

        Return the data in valid JSON format matching the LinkedInProfileData schema.
        If any section is not available, use empty lists or null values.
        Be thorough but don't get stuck - if something takes too long, move on.
        Focus on getting complete and accurate data rather than speed.
        """
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the browser-use client.
        
        Returns:
            Dictionary containing health check results
        """
        try:
            # Test LLM connectivity
            if not self.llm:
                return {
                    "status": "unhealthy",
                    "error": "LLM not initialized",
                    "details": "Azure OpenAI LLM is not properly configured"
                }
            
            # Test browser initialization (quick test)
            browser = None
            try:
                browser = Browser(
                    config=BrowserConfig(
                        headless=True,
                        disable_security=True,
                    )
                )
                
                # Just test that we can create a browser instance
                await browser.close()
                
                return {
                    "status": "healthy",
                    "llm_configured": True,
                    "browser_available": True,
                    "credentials_configured": bool(
                        settings.linkedin.email and
                        settings.linkedin.password
                    )
                }
                
            except Exception as browser_error:
                return {
                    "status": "degraded",
                    "llm_configured": True,
                    "browser_available": False,
                    "browser_error": str(browser_error),
                    "credentials_configured": bool(
                        settings.linkedin.email and
                        settings.linkedin.password
                    )
                }
                
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "details": "Failed to perform health check"
            }


# Global client instance
_browser_use_client = None


def get_browser_use_client() -> BrowserUseClient:
    """
    Get the global browser-use client instance.
    
    Returns:
        BrowserUseClient instance
    """
    global _browser_use_client
    
    if _browser_use_client is None:
        _browser_use_client = BrowserUseClient()
    
    return _browser_use_client


async def extract_linkedin_profile(profile_url: str) -> Optional[Dict[str, Any]]:
    """
    Convenience function to extract a LinkedIn profile.
    
    Args:
        profile_url: The LinkedIn profile URL to extract
        
    Returns:
        Dictionary containing extracted profile data, or None if extraction fails
    """
    client = get_browser_use_client()
    return await client.extract_linkedin_profile(profile_url)


async def browser_use_health_check() -> Dict[str, Any]:
    """
    Convenience function to perform a browser-use health check.
    
    Returns:
        Dictionary containing health check results
    """
    client = get_browser_use_client()
    return await client.health_check() 