"""
Browser-Use client for LinkedIn profile extraction.

This module provides functionality to extract LinkedIn profiles using the browser-use
Python package for web automation and Azure OpenAI for intelligent extraction.

Enhanced with comprehensive error handling, anti-detection measures, and debugging capabilities.
"""

import asyncio
import json
import os
import random
import time
from typing import Optional, Dict, Any, Tu<PERSON>
from pydantic import SecretStr

from browser_use import Agent, Browser, BrowserConfig, Controller
from langchain_openai import AzureChatOpenAI

from common.models import LinkedInProfileData
from common.settings import settings
from common.logging import get_logger

logger = get_logger(__name__)


class LinkedInLoginError(Exception):
    """Custom exception for LinkedIn login failures."""
    def __init__(self, message: str, error_type: str = "unknown", details: Optional[Dict] = None):
        super().__init__(message)
        self.error_type = error_type
        self.details = details or {}


class BrowserUseClient:
    """
    Enhanced client for LinkedIn profile extraction using browser automation.

    Features:
    - Anti-detection measures for LinkedIn
    - Comprehensive error handling
    - Debugging capabilities with non-headless mode
    - Retry logic with exponential backoff
    - Enhanced logging for troubleshooting
    """

    def __init__(self):
        """Initialize the browser-use client with enhanced configuration."""
        self.llm = None
        self.browser = None
        self.controller = None
        self._initialized = False

        # Configuration options
        self.debug_mode = settings.debug or os.getenv('LINKEDIN_DEBUG', 'false').lower() == 'true'
        self.headless_mode = os.getenv('LINKEDIN_HEADLESS', 'true').lower() == 'true'
        self.max_retries = int(os.getenv('LINKEDIN_MAX_RETRIES', '3'))
        self.base_delay = float(os.getenv('LINKEDIN_BASE_DELAY', '2.0'))

        # Anti-detection settings
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]

        self._initialize_llm()
    
    def _initialize_llm(self):
        """Initialize Azure OpenAI LLM for the browser agent."""
        try:
            import os
            from dotenv import load_dotenv
            load_dotenv()

            self.llm = AzureChatOpenAI(
                model=os.getenv('AZURE_OPENAI_DEPLOYMENT_NAME', ''),
                api_version=os.getenv('AZURE_OPENAI_API_VERSION', '2024-02-15-preview'),
                azure_endpoint=os.getenv('AZURE_OPENAI_ENDPOINT', ''),
                api_key=SecretStr(os.getenv('AZURE_OPENAI_API_KEY', '')),
                temperature=float(os.getenv('AZURE_OPENAI_TEMPERATURE', '0.0')),
            )
            logger.info("Azure OpenAI LLM initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Azure OpenAI LLM: {e}")
            raise

    def _get_enhanced_browser_config(self) -> BrowserConfig:
        """
        Create enhanced browser configuration with anti-detection measures.

        Returns:
            BrowserConfig with optimized settings for LinkedIn
        """
        # Select random user agent for this session
        user_agent = random.choice(self.user_agents)

        logger.info(f"Configuring browser - Headless: {self.headless_mode}, Debug: {self.debug_mode}")

        return BrowserConfig(
            headless=self.headless_mode,
            disable_security=True,  # Disable web security for automation
            chrome_instance_path=None,  # Use default Chrome
        )

    async def _random_delay(self, min_seconds: float = 1.0, max_seconds: float = 3.0):
        """Add random delay to mimic human behavior."""
        delay = random.uniform(min_seconds, max_seconds)
        logger.debug(f"Adding random delay: {delay:.2f} seconds")
        await asyncio.sleep(delay)

    async def _detect_linkedin_security_measures(self, agent: Agent) -> Tuple[bool, str, Dict]:
        """
        Detect LinkedIn security measures and anti-bot protections.

        Returns:
            Tuple of (success, error_type, details)
        """
        try:
            # Extract current page content to analyze
            result = await agent.extract_content(
                goal="Check for any security measures, error messages, or verification prompts on the current page"
            )

            content = str(result).lower()

            # Check for various LinkedIn security measures
            if "one-time" in content and "email" in content:
                return False, "email_verification", {"message": "LinkedIn requires one-time email verification"}

            if "captcha" in content or "challenge" in content:
                return False, "captcha", {"message": "CAPTCHA challenge detected"}

            if "rate limit" in content or "too many" in content:
                return False, "rate_limit", {"message": "Rate limiting detected"}

            if "security check" in content or "verify" in content:
                return False, "security_check", {"message": "Security verification required"}

            if "incorrect" in content and "password" in content:
                return False, "invalid_credentials", {"message": "Invalid username or password"}

            if "account" in content and ("locked" in content or "suspended" in content):
                return False, "account_locked", {"message": "Account locked or suspended"}

            # Check if still on login page
            if "sign in" in content and "linkedin.com/login" in content:
                return False, "login_failed", {"message": "Still on login page after login attempt"}

            return True, "none", {"message": "No security measures detected"}

        except Exception as e:
            logger.error(f"Error detecting security measures: {e}")
            return False, "detection_error", {"message": f"Error during detection: {str(e)}"}

    async def extract_linkedin_profile(self, profile_url: str) -> Optional[Dict[str, Any]]:
        """
        Extract LinkedIn profile data with enhanced error handling and retry logic.

        Args:
            profile_url: The LinkedIn profile URL to extract

        Returns:
            Dictionary containing extracted profile data, or None if extraction fails
        """
        logger.info(f"Starting enhanced LinkedIn profile extraction for: {profile_url}")

        for attempt in range(self.max_retries):
            browser = None
            try:
                logger.info(f"Extraction attempt {attempt + 1}/{self.max_retries}")

                # Initialize browser with enhanced configuration
                logger.info("Initializing browser with anti-detection measures...")
                browser_config = self._get_enhanced_browser_config()
                browser = Browser(config=browser_config)

                # Add random delay before starting
                await self._random_delay(1.0, 2.0)

                # Create controller with our data model
                controller = Controller(output_model=LinkedInProfileData)

                # Define the enhanced extraction task
                task = self._create_enhanced_extraction_task(profile_url)

                # Prepare credentials
                if not settings.linkedin.email or not settings.linkedin.password:
                    raise LinkedInLoginError(
                        "LinkedIn credentials not configured",
                        error_type="missing_credentials",
                        details={"email_set": bool(settings.linkedin.email), "password_set": bool(settings.linkedin.password)}
                    )

                sensitive_data = {
                    'username': settings.linkedin.email,
                    'password': settings.linkedin.password.get_secret_value()
                }

                # Create and run the agent with enhanced monitoring
                logger.info("Creating extraction agent with enhanced monitoring...")
                agent = Agent(
                    task=task,
                    llm=self.llm,
                    browser=browser,
                    controller=controller,
                    sensitive_data=sensitive_data
                )

                # Execute extraction with monitoring
                result = await self._execute_extraction_with_monitoring(agent, profile_url)

                if result:
                    logger.info("LinkedIn profile extraction completed successfully")
                    return result
                else:
                    logger.warning(f"Extraction attempt {attempt + 1} returned empty result")

            except LinkedInLoginError as e:
                logger.error(f"LinkedIn login error on attempt {attempt + 1}: {e.error_type} - {e}")

                # Don't retry for certain error types
                if e.error_type in ["missing_credentials", "account_locked", "invalid_credentials"]:
                    logger.error(f"Non-retryable error: {e.error_type}")
                    break

                # For retryable errors, wait with exponential backoff
                if attempt < self.max_retries - 1:
                    delay = self.base_delay * (2 ** attempt) + random.uniform(0, 1)
                    logger.info(f"Retrying in {delay:.2f} seconds...")
                    await asyncio.sleep(delay)

            except Exception as e:
                logger.error(f"Unexpected error during extraction attempt {attempt + 1}: {e}", exc_info=True)

                if attempt < self.max_retries - 1:
                    delay = self.base_delay * (2 ** attempt)
                    logger.info(f"Retrying in {delay:.2f} seconds...")
                    await asyncio.sleep(delay)

            finally:
                # Always clean up browser
                if browser:
                    try:
                        await browser.stop()
                        logger.info("Browser session stopped successfully")
                    except Exception as e:
                        logger.warning(f"Error stopping browser session: {e}")

        logger.error(f"All {self.max_retries} extraction attempts failed")
        return None

    async def _execute_extraction_with_monitoring(self, agent: Agent, profile_url: str) -> Optional[Dict[str, Any]]:
        """
        Execute extraction with comprehensive monitoring and error detection.

        Args:
            agent: The browser agent
            profile_url: LinkedIn profile URL

        Returns:
            Extracted profile data or None if failed
        """
        try:
            logger.info("Starting monitored extraction process...")

            # Run the agent
            history = await agent.run()
            result = history.final_result()

            if not result:
                logger.warning("Agent returned empty result")
                return None

            # Parse and validate the result
            try:
                if isinstance(result, str):
                    linkedin_data = LinkedInProfileData.model_validate_json(result)
                else:
                    linkedin_data = LinkedInProfileData.model_validate(result)

                logger.info(
                    f"Successfully extracted profile for: {linkedin_data.full_name}",
                    profile_url=profile_url,
                    work_experience_count=len(linkedin_data.work_experience),
                    education_count=len(linkedin_data.education),
                    skills_count=len(linkedin_data.skills)
                )

                return linkedin_data.model_dump()

            except Exception as e:
                logger.error(f"Failed to parse extraction result: {e}", raw_result=str(result))
                return None

        except Exception as e:
            logger.error(f"Error during monitored extraction: {e}", profile_url=profile_url)

            # Try to detect what went wrong
            try:
                success, error_type, details = await self._detect_linkedin_security_measures(agent)
                if not success:
                    raise LinkedInLoginError(
                        details.get("message", "Unknown LinkedIn security measure"),
                        error_type=error_type,
                        details=details
                    )
            except Exception as detection_error:
                logger.warning(f"Could not detect security measures: {detection_error}")

            return None
    
    def _create_enhanced_extraction_task(self, profile_url: str) -> str:
        """
        Create enhanced extraction task with better error handling and human-like behavior.

        Args:
            profile_url: The LinkedIn profile URL to extract

        Returns:
            Enhanced task string for the browser agent
        """
        return f"""
        You are an expert LinkedIn profile data extractor. Your goal is to extract comprehensive profile information while behaving like a human user.

        IMPORTANT: Take your time, be patient, and handle errors gracefully. If you encounter any security measures, report them clearly.

        Step-by-step process:

        1. **Navigate to LinkedIn Login**
           - Go to https://www.linkedin.com/login
           - Wait for the page to fully load (3-5 seconds)
           - Check if the login form is visible

        2. **Handle Login Process**
           - Enter username/email in the appropriate field
           - Wait 1-2 seconds (human-like behavior)
           - Enter password in the password field
           - Wait 1-2 seconds
           - Click the "Sign in" button
           - Wait for login to complete (up to 10 seconds)

        3. **Detect and Handle Security Measures**
           - If you see "one-time email link" or email verification: STOP and report this
           - If you see CAPTCHA: STOP and report this
           - If you see "rate limit" or "too many attempts": STOP and report this
           - If login fails: Try once more, then STOP and report the issue

        4. **Navigate to Profile**
           - Once logged in successfully, navigate to: {profile_url}
           - Wait for the profile page to fully load (5-10 seconds)
           - Scroll down slowly to trigger lazy loading

        5. **Extract Profile Data**
           - Full name (from profile header)
           - Headline/title (below name)
           - Location (if visible)
           - About section (click "Show more" if needed)
           - Current company and position

        6. **Extract Work Experience**
           - Look for "Experience" section
           - Click "Show all experiences" if available
           - For each role, extract: company, title, dates, description
           - Handle multiple roles at same company

        7. **Extract Education**
           - Look for "Education" section
           - Extract: institution, degree, field of study, dates

        8. **Extract Additional Sections (if available)**
           - Skills (expand if needed)
           - Certifications
           - Volunteer experience
           - Projects
           - Languages

        ERROR HANDLING:
        - If any step fails, try once more with a 2-3 second wait
        - If you encounter security measures, immediately stop and report the specific issue
        - If profile is private or restricted, report this clearly
        - If any section is not available, use empty values

        Return the data in valid JSON format. Be thorough but don't get stuck on any single section.
        """
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform enhanced health check on the browser-use client.

        Returns:
            Dictionary containing comprehensive health check results
        """
        try:
            # Test LLM connectivity
            if not self.llm:
                return {
                    "status": "unhealthy",
                    "error": "LLM not initialized",
                    "details": "Azure OpenAI LLM is not properly configured"
                }

            # Test browser initialization with enhanced config
            browser = None
            try:
                browser = self._get_enhanced_browser_session()

                # Just test that we can create a browser session
                await browser.stop()

                return {
                    "status": "healthy",
                    "llm_configured": True,
                    "browser_available": True,
                    "configuration": {
                        "headless_mode": self.headless_mode,
                        "debug_mode": self.debug_mode,
                        "max_retries": self.max_retries,
                        "base_delay": self.base_delay,
                        "user_agents_count": len(self.user_agents)
                    },
                    "credentials_configured": bool(
                        settings.linkedin.email and
                        settings.linkedin.password
                    ),
                    "anti_detection_enabled": True
                }
                
            except Exception as browser_error:
                return {
                    "status": "degraded",
                    "llm_configured": True,
                    "browser_available": False,
                    "browser_error": str(browser_error),
                    "configuration": {
                        "headless_mode": self.headless_mode,
                        "debug_mode": self.debug_mode,
                        "max_retries": self.max_retries,
                        "base_delay": self.base_delay
                    },
                    "credentials_configured": bool(
                        settings.linkedin.email and
                        settings.linkedin.password
                    ),
                    "anti_detection_enabled": True
                }

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "details": "Failed to perform enhanced health check",
                "configuration": {
                    "headless_mode": getattr(self, 'headless_mode', 'unknown'),
                    "debug_mode": getattr(self, 'debug_mode', 'unknown')
                }
            }


# Global client instance
_browser_use_client = None


def get_browser_use_client() -> BrowserUseClient:
    """
    Get the global browser-use client instance.
    
    Returns:
        BrowserUseClient instance
    """
    global _browser_use_client
    
    if _browser_use_client is None:
        _browser_use_client = BrowserUseClient()
    
    return _browser_use_client


async def extract_linkedin_profile(profile_url: str) -> Optional[Dict[str, Any]]:
    """
    Convenience function to extract a LinkedIn profile.
    
    Args:
        profile_url: The LinkedIn profile URL to extract
        
    Returns:
        Dictionary containing extracted profile data, or None if extraction fails
    """
    client = get_browser_use_client()
    return await client.extract_linkedin_profile(profile_url)


async def browser_use_health_check() -> Dict[str, Any]:
    """
    Convenience function to perform a browser-use health check.
    
    Returns:
        Dictionary containing health check results
    """
    client = get_browser_use_client()
    return await client.health_check() 