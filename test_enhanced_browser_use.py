#!/usr/bin/env python3
"""
Test script for the enhanced browser-use LinkedIn extraction client.

This script tests the new features including:
- Non-headless mode for debugging
- Enhanced error handling
- Anti-detection measures
- Retry logic
"""

import asyncio
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
load_dotenv()

from services.api.clients.browser_use import BrowserUseClient, LinkedInLoginError
from common.logging import configure_logging, get_logger

# Configure logging
configure_logging(
    service_name="test-browser-use",
    log_level="DEBUG",
    json_logs=False
)

logger = get_logger(__name__)


async def test_health_check():
    """Test the enhanced health check functionality."""
    logger.info("Testing enhanced health check...")
    
    client = BrowserUseClient()
    health_result = await client.health_check()
    
    logger.info("Health check result:")
    for key, value in health_result.items():
        logger.info(f"  {key}: {value}")
    
    return health_result.get("status") in ["healthy", "degraded"]


async def test_linkedin_extraction():
    """Test LinkedIn profile extraction with enhanced error handling."""
    logger.info("Testing LinkedIn profile extraction...")
    
    # Test URL - you can change this to any LinkedIn profile
    test_url = "https://www.linkedin.com/in/yashkhivasara/"
    
    client = BrowserUseClient()
    
    try:
        result = await client.extract_linkedin_profile(test_url)
        
        if result:
            logger.info("Extraction successful!")
            logger.info(f"Profile name: {result.get('full_name', 'N/A')}")
            logger.info(f"Headline: {result.get('headline', 'N/A')}")
            logger.info(f"Location: {result.get('location', 'N/A')}")
            logger.info(f"Work experience entries: {len(result.get('work_experience', []))}")
            logger.info(f"Education entries: {len(result.get('education', []))}")
            return True
        else:
            logger.warning("Extraction returned no data")
            return False
            
    except LinkedInLoginError as e:
        logger.error(f"LinkedIn login error: {e.error_type} - {e}")
        logger.error(f"Error details: {e.details}")
        return False
        
    except Exception as e:
        logger.error(f"Unexpected error during extraction: {e}", exc_info=True)
        return False


async def main():
    """Main test function."""
    logger.info("Starting enhanced browser-use client tests...")
    
    # Test 1: Health check
    logger.info("\n" + "="*50)
    logger.info("TEST 1: Health Check")
    logger.info("="*50)
    
    health_ok = await test_health_check()
    if not health_ok:
        logger.error("Health check failed - stopping tests")
        return False
    
    # Test 2: LinkedIn extraction
    logger.info("\n" + "="*50)
    logger.info("TEST 2: LinkedIn Profile Extraction")
    logger.info("="*50)
    
    extraction_ok = await test_linkedin_extraction()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    logger.info(f"Health check: {'PASS' if health_ok else 'FAIL'}")
    logger.info(f"LinkedIn extraction: {'PASS' if extraction_ok else 'FAIL'}")
    
    if health_ok and extraction_ok:
        logger.info("All tests passed! ✅")
        return True
    else:
        logger.warning("Some tests failed. Check the logs above for details.")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test script failed: {e}", exc_info=True)
        sys.exit(1)
